import { useMutation } from "@tanstack/react-query";
import { toast } from  '@camped-ai/ui';
import type { ExportConfig } from "../../components/hotel/pricing/import-export/utils/export-utils";

interface ExportPricingRequest {
  hotelId: string;
  config: ExportConfig;
}

interface ExportPricingResponse {
  success: boolean;
  filename?: string;
  totalRows?: number;
  fileSize?: string;
  format?: string;
}

const exportPricingData = async ({ hotelId, config }: ExportPricingRequest): Promise<ExportPricingResponse> => {
  const response = await fetch(`/admin/hotel-management/hotels/${hotelId}/pricing/export`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(config),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Export failed with status ${response.status}`);
  }

  // Check if response is JSON (error) or file (success)
  const contentType = response.headers.get('content-type');
  
  if (contentType?.includes('application/json')) {
    // This is an error response
    const errorData = await response.json();
    throw new Error(errorData.message || 'Export failed');
  }

  // This is a file download
  const contentDisposition = response.headers.get('content-disposition');
  const filename = contentDisposition?.match(/filename="([^"]+)"/)?.[1] || 'pricing_export';
  const contentLength = response.headers.get('content-length');
  
  // Create blob and download
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  
  // Create temporary download link
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up
  window.URL.revokeObjectURL(url);

  // Calculate file size for display
  const fileSizeBytes = blob.size;
  const fileSize = fileSizeBytes < 1024 
    ? `${fileSizeBytes} B`
    : fileSizeBytes < 1024 * 1024
    ? `${(fileSizeBytes / 1024).toFixed(1)} KB`
    : `${(fileSizeBytes / (1024 * 1024)).toFixed(1)} MB`;

  return {
    success: true,
    filename,
    fileSize,
    format: config.format,
    totalRows: undefined // We don't get this info from the download response
  };
};

export const useAdminExportPricing = () => {
  return useMutation({
    mutationFn: exportPricingData,
    onSuccess: (data) => {
      toast.success("Export Successful", {
        description: `File "${data.filename}" has been downloaded (${data.fileSize})`,
        duration: 5000,
      });
    },
    onError: (error: Error) => {
      console.error("Export error:", error);
      toast.error("Export Failed", {
        description: error.message || "An unexpected error occurred during export",
        duration: 7000,
      });
    },
  });
};

// Hook for getting export progress (if needed for large exports)
export const useExportProgress = () => {
  // This could be implemented with WebSockets or polling for large exports
  // For now, we'll use a simple state management approach
  
  return {
    // Placeholder for future implementation
    progress: 0,
    isExporting: false,
    stage: 'idle'
  };
};

// Utility hook for validating export configuration
export const useExportValidation = () => {
  const validateConfig = (config: ExportConfig): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Check if at least one data type is selected
    if (!config.includeBasePricing && !config.includeSeasonalPricing) {
      errors.push('At least one pricing type must be included');
    }

    // Validate date range if provided
    if (config.filters.dateRange) {
      const startDate = new Date(config.filters.dateRange.start);
      const endDate = new Date(config.filters.dateRange.end);

      if (isNaN(startDate.getTime())) {
        errors.push('Invalid start date');
      }

      if (isNaN(endDate.getTime())) {
        errors.push('Invalid end date');
      }

      if (startDate >= endDate) {
        errors.push('End date must be after start date');
      }

      // Check if date range is too large (more than 2 years)
      const daysDiff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
      if (daysDiff > 730) {
        errors.push('Date range cannot exceed 2 years');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  return { validateConfig };
};

// Hook for estimating export size
export const useExportEstimation = () => {
  const estimateExportSize = (
    config: ExportConfig,
    availableData: {
      roomTypesCount: number;
      occupancyTypesCount: number;
      mealPlansCount: number;
      seasonalPeriodsCount: number;
      currenciesCount: number;
    }
  ): { estimatedRows: number; estimatedFileSize: string } => {
    let baseRows = availableData.roomTypesCount * availableData.occupancyTypesCount * availableData.mealPlansCount;
    let seasonalRows = baseRows * availableData.seasonalPeriodsCount;
    
    let totalRows = 0;
    if (config.includeBasePricing) totalRows += baseRows;
    if (config.includeSeasonalPricing) totalRows += seasonalRows;
    
    // Apply filters
    if (config.filters.currencies.length > 0) {
      totalRows = Math.round(totalRows * (config.filters.currencies.length / availableData.currenciesCount));
    }
    
    if (config.filters.roomTypes.length > 0) {
      totalRows = Math.round(totalRows * (config.filters.roomTypes.length / availableData.roomTypesCount));
    }
    
    if (config.filters.seasonalPeriods.length > 0) {
      const seasonalRatio = config.filters.seasonalPeriods.length / availableData.seasonalPeriodsCount;
      totalRows = Math.round(totalRows * seasonalRatio);
    }

    // Estimate file size based on format and columns
    const columnsCount = config.exportType === 'summary' ? 12 : (config.includeCostMargin ? 35 : 15);
    const avgCellSize = 10; // Average characters per cell
    const estimatedBytes = totalRows * columnsCount * avgCellSize;
    
    // Add overhead for format
    const overhead = config.format === 'excel' ? 1.5 : 1.1;
    const finalBytes = estimatedBytes * overhead;
    
    const estimatedFileSize = finalBytes < 1024 
      ? `${Math.round(finalBytes)} B`
      : finalBytes < 1024 * 1024
      ? `${(finalBytes / 1024).toFixed(1)} KB`
      : `${(finalBytes / (1024 * 1024)).toFixed(1)} MB`;

    return {
      estimatedRows: Math.max(1, totalRows),
      estimatedFileSize
    };
  };

  return { estimateExportSize };
};
