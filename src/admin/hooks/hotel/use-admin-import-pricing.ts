import React, { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";
import { CSVExcelUtils } from "../../components/hotel/pricing/import-export/utils/csv-excel-utils";
import type { ImportConfig, ImportPreview } from "../../components/hotel/pricing/import-export/utils/import-utils";

interface ImportPricingRequest {
  hotelId: string;
  config: ImportConfig;
}

interface ImportPreviewRequest {
  hotelId: string;
  config: ImportConfig;
}

interface ImportPricingResponse {
  success: boolean;
  result: {
    totalProcessed: number;
    created: number;
    updated: number;
    skipped: number;
    errors: string[];
    warnings: string[];
  };
}

interface ImportPreviewResponse {
  success: boolean;
  preview: ImportPreview;
  canProceed: boolean;
  validationSummary: string;
}

// Hook for previewing import data
export const useAdminImportPreview = () => {
  return useMutation({
    mutationFn: async ({ hotelId, config }: ImportPreviewRequest): Promise<ImportPreviewResponse> => {
      if (!config.file) {
        throw new Error("No file selected for import");
      }

      // Parse the file
      const fileData = await CSVExcelUtils.parseFile(config.file);

      const response = await fetch(`/admin/hotel-management/hotels/${hotelId}/pricing/import/preview`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          importType: config.importType,
          updateMode: config.updateMode,
          currency: config.currency,
          validateDuplicates: config.validateDuplicates,
          fileData
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Preview failed with status ${response.status}`);
      }

      return response.json();
    },
    onError: (error: Error) => {
      console.error("Import preview error:", error);
      toast.error("Preview Failed", {
        description: error.message || "An unexpected error occurred during preview",
        duration: 7000,
      });
    },
  });
};

// Hook for importing pricing data
export const useAdminImportPricing = () => {
  return useMutation({
    mutationFn: async ({ hotelId, config }: ImportPricingRequest): Promise<ImportPricingResponse> => {
      if (!config.file) {
        throw new Error("No file selected for import");
      }

      // Parse the file
      const fileData = await CSVExcelUtils.parseFile(config.file);

      const response = await fetch(`/admin/hotel-management/hotels/${hotelId}/pricing/import`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          importType: config.importType,
          updateMode: config.updateMode,
          currency: config.currency,
          validateDuplicates: config.validateDuplicates,
          fileData
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Import failed with status ${response.status}`);
      }

      return response.json();
    },
    onSuccess: (data) => {
      const { result } = data;
      const hasErrors = result.errors.length > 0;
      const hasWarnings = result.warnings.length > 0;

      if (hasErrors) {
        toast.error("Import Completed with Errors", {
          description: `${result.created} created, ${result.updated} updated, ${result.errors.length} errors`,
          duration: 10000,
        });
      } else if (hasWarnings) {
        toast.warning("Import Completed with Warnings", {
          description: `${result.created} created, ${result.updated} updated, ${result.warnings.length} warnings`,
          duration: 8000,
        });
      } else {
        toast.success("Import Successful", {
          description: `${result.created} records created, ${result.updated} records updated`,
          duration: 5000,
        });
      }
    },
    onError: (error: Error) => {
      console.error("Import error:", error);
      toast.error("Import Failed", {
        description: error.message || "An unexpected error occurred during import",
        duration: 7000,
      });
    },
  });
};

// Hook for managing import state and progress
export const useImportProgress = () => {
  const [isImporting, setIsImporting] = useState(false);
  const [progress, setProgress] = useState(0);
  const [stage, setStage] = useState('');
  const [errors, setErrors] = useState<string[]>([]);
  const [warnings, setWarnings] = useState<string[]>([]);

  const startImport = React.useCallback(() => {
    setIsImporting(true);
    setProgress(0);
    setStage('Initializing import...');
    setErrors([]);
    setWarnings([]);
  }, []);

  const updateProgress = React.useCallback((newProgress: number, newStage: string) => {
    setProgress(newProgress);
    setStage(newStage);
  }, []);

  const addError = React.useCallback((error: string) => {
    setErrors(prev => [...prev, error]);
  }, []);

  const addWarning = React.useCallback((warning: string) => {
    setWarnings(prev => [...prev, warning]);
  }, []);

  const completeImport = React.useCallback(() => {
    setIsImporting(false);
    setProgress(100);
    setStage('Import completed');
  }, []);

  const resetImport = React.useCallback(() => {
    setIsImporting(false);
    setProgress(0);
    setStage('');
    setErrors([]);
    setWarnings([]);
  }, []);

  return {
    isImporting,
    progress,
    stage,
    errors,
    warnings,
    startImport,
    updateProgress,
    addError,
    addWarning,
    completeImport,
    resetImport
  };
};

// Hook for validating import files
export const useImportFileValidation = () => {
  const validateFile = React.useCallback(async (file: File): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    preview?: any;
  }> => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate file format
    const formatValidation = CSVExcelUtils.validateFileFormat(file);
    if (!formatValidation.valid) {
      errors.push(formatValidation.error!);
      return { isValid: false, errors, warnings };
    }

    try {
      // Parse file to check structure
      const parsedData = await CSVExcelUtils.parseFile(file);

      // Check if file has data
      if (parsedData.totalRows === 0) {
        errors.push('File contains no data rows');
        return { isValid: false, errors, warnings };
      }

      // Check for required headers
      const requiredHeaders = ['room_type', 'occupancy_type', 'meal_plan', 'currency'];
      const missingHeaders = requiredHeaders.filter(header => 
        !parsedData.headers.some(h => h.toLowerCase().includes(header.toLowerCase()))
      );

      if (missingHeaders.length > 0) {
        errors.push(`Missing required columns: ${missingHeaders.join(', ')}`);
      }

      // Check for price columns
      const priceHeaders = ['price_mon', 'price_tue', 'price_wed', 'price_thu', 'price_fri', 'price_sat', 'price_sun'];
      const hasPriceColumns = priceHeaders.some(header => 
        parsedData.headers.some(h => h.toLowerCase().includes(header.toLowerCase()))
      );

      if (!hasPriceColumns) {
        errors.push('No weekday price columns found');
      }

      // Warnings for large files
      if (parsedData.totalRows > 1000) {
        warnings.push(`Large file detected (${parsedData.totalRows} rows). Import may take several minutes.`);
      }

      // Check for seasonal data
      const hasSeasonalColumns = parsedData.headers.some(h => 
        h.toLowerCase().includes('seasonal') || h.toLowerCase().includes('season')
      );

      if (hasSeasonalColumns) {
        warnings.push('Seasonal pricing data detected. Make sure seasonal period names and dates are correct.');
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        preview: {
          totalRows: parsedData.totalRows,
          headers: parsedData.headers,
          sampleRows: parsedData.rows.slice(0, 3)
        }
      };

    } catch (error) {
      errors.push(`Failed to parse file: ${error.message}`);
      return { isValid: false, errors, warnings };
    }
  }, []);

  return { validateFile };
};

// Hook for generating import templates
export const useImportTemplate = () => {
  const generateTemplate = React.useCallback((
    templateType: 'base_pricing' | 'seasonal_pricing' | 'both',
    format: 'csv' | 'excel' = 'excel'
  ) => {
    const templateData = CSVExcelUtils.generateImportTemplate(templateType);
    const filename = `pricing_import_template_${templateType}.${format === 'csv' ? 'csv' : 'xlsx'}`;
    
    CSVExcelUtils.downloadFile({
      filename,
      format,
      data: templateData
    });

    toast.success("Template Downloaded", {
      description: `Template file "${filename}" has been downloaded`,
      duration: 3000,
    });
  }, []);

  return { generateTemplate };
};
