import React, { useState } from "react";
import { FocusModal, <PERSON><PERSON>, Badge, Tabs } from "@camped-ai/ui";
import { CheckCircle, XCircle, AlertTriangle, Eye, Upload } from "lucide-react";
import type { ImportPreview } from "./utils/import-utils";

interface ImportPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProceed: () => void;
  previewData: ImportPreview | null;
  isLoading?: boolean;
}

export const ImportPreviewModal: React.FC<ImportPreviewModalProps> = ({
  isOpen,
  onClose,
  onProceed,
  previewData,
  isLoading = false,
}) => {
  const [activeTab, setActiveTab] = useState("summary");

  if (!previewData && !isLoading) {
    return null;
  }

  const canProceed =
    previewData && previewData.validRows > 0 && previewData.errors.length === 0;

  const getStatusIcon = (isValid: boolean) => {
    return isValid ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <XCircle className="h-4 w-4 text-red-600" />
    );
  };

  const getStatusBadge = (isValid: boolean) => {
    return (
      <Badge variant={isValid ? "green" : "red"} size="small">
        {isValid ? "Valid" : "Invalid"}
      </Badge>
    );
  };

  return (
    <FocusModal open={isOpen} onOpenChange={onClose}>
      <FocusModal.Content className="max-w-4xl max-h-[90vh]">
        <FocusModal.Header>
          <FocusModal.Title className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Import Preview & Validation
          </FocusModal.Title>
        </FocusModal.Header>

        <FocusModal.Body className="space-y-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">
                Validating import data...
              </span>
            </div>
          ) : previewData ? (
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <Tabs.List>
                <Tabs.Trigger value="summary">Summary</Tabs.Trigger>
                <Tabs.Trigger value="preview">Data Preview</Tabs.Trigger>
                <Tabs.Trigger value="errors">
                  Errors{" "}
                  {previewData.errors.length > 0 &&
                    `(${previewData.errors.length})`}
                </Tabs.Trigger>
                <Tabs.Trigger value="warnings">
                  Warnings{" "}
                  {previewData.warnings.length > 0 &&
                    `(${previewData.warnings.length})`}
                </Tabs.Trigger>
                {previewData.duplicateSeasons.length > 0 && (
                  <Tabs.Trigger value="duplicates">
                    Duplicates ({previewData.duplicateSeasons.length})
                  </Tabs.Trigger>
                )}
              </Tabs.List>

              {/* Summary Tab */}
              <Tabs.Content value="summary" className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="text-2xl font-bold text-blue-800">
                      {previewData.totalRows}
                    </div>
                    <div className="text-sm text-blue-600">Total Rows</div>
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="text-2xl font-bold text-green-800">
                      {previewData.validRows}
                    </div>
                    <div className="text-sm text-green-600">Valid Rows</div>
                  </div>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="text-2xl font-bold text-red-800">
                      {previewData.invalidRows}
                    </div>
                    <div className="text-sm text-red-600">Invalid Rows</div>
                  </div>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="text-2xl font-bold text-yellow-800">
                      {previewData.warnings.length}
                    </div>
                    <div className="text-sm text-yellow-600">Warnings</div>
                  </div>
                </div>

                {/* Import Summary */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-800 mb-3">
                    Import Summary
                  </h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-gray-600">Base Pricing Rows:</div>
                      <div className="font-medium">
                        {previewData.summary.basePricingRows}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-600">
                        Seasonal Pricing Rows:
                      </div>
                      <div className="font-medium">
                        {previewData.summary.seasonalPricingRows}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-600">Room Types:</div>
                      <div className="font-medium">
                        {previewData.summary.uniqueRoomTypes.length}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-600">Occupancy Types:</div>
                      <div className="font-medium">
                        {previewData.summary.uniqueOccupancyTypes.length}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-600">Meal Plans:</div>
                      <div className="font-medium">
                        {previewData.summary.uniqueMealPlans.length}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-600">Seasonal Periods:</div>
                      <div className="font-medium">
                        {previewData.summary.uniqueSeasons.length}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Status */}
                <div
                  className={`border rounded-lg p-4 ${
                    canProceed
                      ? "bg-green-50 border-green-200"
                      : "bg-red-50 border-red-200"
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    {canProceed ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-600" />
                    )}
                    <h4
                      className={`font-medium ${
                        canProceed ? "text-green-800" : "text-red-800"
                      }`}
                    >
                      {canProceed ? "Ready to Import" : "Cannot Import"}
                    </h4>
                  </div>
                  <p
                    className={`text-sm ${
                      canProceed ? "text-green-700" : "text-red-700"
                    }`}
                  >
                    {canProceed
                      ? `${
                          previewData.validRows
                        } rows are ready to be imported. ${
                          previewData.warnings.length > 0
                            ? "Please review warnings before proceeding."
                            : ""
                        }`
                      : `${previewData.errors.length} error(s) must be fixed before import can proceed.`}
                  </p>
                </div>
              </Tabs.Content>

              {/* Data Preview Tab */}
              <Tabs.Content value="preview" className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                  Showing first 10 rows of import data
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Row
                        </th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Status
                        </th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Room Type
                        </th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Occupancy
                        </th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Meal Plan
                        </th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Currency
                        </th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Type
                        </th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Issues
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {previewData.preview.map((row) => (
                        <tr
                          key={row.rowNumber}
                          className={row.isValid ? "" : "bg-red-50"}
                        >
                          <td className="px-3 py-2 text-sm text-gray-900">
                            {row.rowNumber}
                          </td>
                          <td className="px-3 py-2">
                            {getStatusBadge(row.isValid)}
                          </td>
                          <td className="px-3 py-2 text-sm text-gray-900">
                            {row.parsedData?.room_type || "-"}
                          </td>
                          <td className="px-3 py-2 text-sm text-gray-900">
                            {row.parsedData?.occupancy_type || "-"}
                          </td>
                          <td className="px-3 py-2 text-sm text-gray-900">
                            {row.parsedData?.meal_plan || "-"}
                          </td>
                          <td className="px-3 py-2 text-sm text-gray-900">
                            {row.parsedData?.currency || "-"}
                          </td>
                          <td className="px-3 py-2 text-sm text-gray-900">
                            <Badge
                              variant={
                                row.parsedData?.pricing_type === "seasonal"
                                  ? "purple"
                                  : "blue"
                              }
                              size="small"
                            >
                              {row.parsedData?.pricing_type || "unknown"}
                            </Badge>
                          </td>
                          <td className="px-3 py-2 text-sm">
                            {row.errors.length > 0 && (
                              <div className="text-red-600 text-xs">
                                {row.errors.length} error(s)
                              </div>
                            )}
                            {row.warnings.length > 0 && (
                              <div className="text-yellow-600 text-xs">
                                {row.warnings.length} warning(s)
                              </div>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </Tabs.Content>

              {/* Errors Tab */}
              <Tabs.Content value="errors" className="space-y-4">
                {previewData.errors.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    No errors found in the import data.
                  </div>
                ) : (
                  <div className="space-y-3">
                    {previewData.errors.map((error, index) => (
                      <div
                        key={index}
                        className="bg-red-50 border border-red-200 rounded-lg p-4"
                      >
                        <div className="flex items-start gap-3">
                          <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                          <div className="flex-1">
                            <div className="font-medium text-red-800">
                              Row {error.rowNumber}: {error.message}
                            </div>
                            {error.field && (
                              <div className="text-sm text-red-600 mt-1">
                                Field: {error.field}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </Tabs.Content>

              {/* Warnings Tab */}
              <Tabs.Content value="warnings" className="space-y-4">
                {previewData.warnings.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    No warnings found in the import data.
                  </div>
                ) : (
                  <div className="space-y-3">
                    {previewData.warnings.map((warning, index) => (
                      <div
                        key={index}
                        className="bg-yellow-50 border border-yellow-200 rounded-lg p-4"
                      >
                        <div className="flex items-start gap-3">
                          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                          <div className="flex-1">
                            <div className="font-medium text-yellow-800">
                              {warning.rowNumber
                                ? `Row ${warning.rowNumber}: `
                                : ""}
                              {warning.message}
                            </div>
                            {warning.field && (
                              <div className="text-sm text-yellow-600 mt-1">
                                Field: {warning.field}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </Tabs.Content>

              {/* Duplicates Tab */}
              {previewData.duplicateSeasons.length > 0 && (
                <Tabs.Content value="duplicates" className="space-y-4">
                  <div className="space-y-3">
                    {previewData.duplicateSeasons.map((duplicate, index) => (
                      <div
                        key={index}
                        className="bg-orange-50 border border-orange-200 rounded-lg p-4"
                      >
                        <div className="flex items-start gap-3">
                          <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
                          <div className="flex-1">
                            <div className="font-medium text-orange-800">
                              Duplicate Seasonal Period: {duplicate.seasonName}
                            </div>
                            <div className="text-sm text-orange-600 mt-1">
                              Date Range: {duplicate.dateRange}
                            </div>
                            <div className="text-sm text-orange-600">
                              Affected Rows: {duplicate.affectedRows.join(", ")}
                            </div>
                            {duplicate.existingSeasonId && (
                              <div className="text-sm text-orange-600">
                                Conflicts with existing season ID:{" "}
                                {duplicate.existingSeasonId}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Tabs.Content>
              )}
            </Tabs>
          ) : null}
        </FocusModal.Body>

        <FocusModal.Footer>
          <div className="flex justify-end gap-2">
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>

            <Button
              onClick={onProceed}
              disabled={!canProceed}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Proceed with Import
            </Button>
          </div>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};
