import React from "react";
import { FocusModal } from "@camped-ai/ui";
import { CheckCircle, XCircle, AlertCircle, Loader2 } from "lucide-react";

export interface ProgressState {
  operation: "export" | "import";
  stage: string;
  progress: number;
  totalItems: number;
  processedItems: number;
  errors: string[];
  warnings: string[];
  isComplete: boolean;
  isError: boolean;
  result?: any;
}

interface ProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  progressState: ProgressState;
  onRetry?: () => void;
}

export const ProgressModal: React.FC<ProgressModalProps> = ({
  isOpen,
  onClose,
  progressState,
  onRetry,
}) => {
  const {
    operation,
    stage,
    progress,
    totalItems,
    processedItems,
    errors,
    warnings,
    isComplete,
    isError,
    result,
  } = progressState;

  const getStatusIcon = () => {
    if (isError) {
      return <XCircle className="h-8 w-8 text-red-500" />;
    }
    if (isComplete) {
      return <CheckCircle className="h-8 w-8 text-green-500" />;
    }
    return <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />;
  };

  const getStatusText = () => {
    if (isError) {
      return `${operation === "export" ? "Export" : "Import"} Failed`;
    }
    if (isComplete) {
      return `${operation === "export" ? "Export" : "Import"} Complete`;
    }
    return `${operation === "export" ? "Exporting" : "Importing"} Data...`;
  };

  const getProgressPercentage = () => {
    if (totalItems === 0) return 0;
    return Math.round((processedItems / totalItems) * 100);
  };

  return (
    <FocusModal open={isOpen} onOpenChange={onClose}>
      <FocusModal.Content className="max-w-md">
        <FocusModal.Header>
          <FocusModal.Title className="flex items-center gap-3">
            {getStatusIcon()}
            {getStatusText()}
          </FocusModal.Title>
        </FocusModal.Header>

        <FocusModal.Body className="space-y-6">
          {/* Progress Section */}
          <div className="space-y-3">
            <div className="flex justify-between text-sm text-gray-600">
              <span>{stage}</span>
              <span>
                {processedItems} / {totalItems}
              </span>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  isError
                    ? "bg-red-500"
                    : isComplete
                    ? "bg-green-500"
                    : "bg-blue-500"
                }`}
                style={{ width: `${getProgressPercentage()}%` }}
              />
            </div>

            <div className="text-center text-sm text-gray-500">
              {getProgressPercentage()}% Complete
            </div>
          </div>

          {/* Results Section (when complete) */}
          {isComplete && result && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-medium text-green-800 mb-2">
                {operation === "export" ? "Export" : "Import"} Summary
              </h4>
              <div className="text-sm text-green-700 space-y-1">
                {operation === "export" ? (
                  <>
                    <div>• {result.totalRows || 0} rows exported</div>
                    <div>• File format: {result.format?.toUpperCase()}</div>
                    <div>• File size: {result.fileSize || "Unknown"}</div>
                  </>
                ) : (
                  <>
                    <div>• {result.totalProcessed || 0} rows processed</div>
                    <div>• {result.created || 0} new records created</div>
                    <div>• {result.updated || 0} records updated</div>
                    {result.skipped > 0 && (
                      <div>• {result.skipped} rows skipped</div>
                    )}
                  </>
                )}
              </div>
            </div>
          )}

          {/* Warnings Section */}
          {warnings.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <h4 className="font-medium text-yellow-800">
                  Warnings ({warnings.length})
                </h4>
              </div>
              <div className="max-h-32 overflow-y-auto">
                {warnings.slice(0, 5).map((warning, index) => (
                  <div key={index} className="text-sm text-yellow-700 mb-1">
                    • {warning}
                  </div>
                ))}
                {warnings.length > 5 && (
                  <div className="text-sm text-yellow-600 italic">
                    ... and {warnings.length - 5} more warnings
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Errors Section */}
          {errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <XCircle className="h-4 w-4 text-red-600" />
                <h4 className="font-medium text-red-800">
                  Errors ({errors.length})
                </h4>
              </div>
              <div className="max-h-32 overflow-y-auto">
                {errors.slice(0, 5).map((error, index) => (
                  <div key={index} className="text-sm text-red-700 mb-1">
                    • {error}
                  </div>
                ))}
                {errors.length > 5 && (
                  <div className="text-sm text-red-600 italic">
                    ... and {errors.length - 5} more errors
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Current Stage Info */}
          {!isComplete && !isError && (
            <div className="text-center text-sm text-gray-500">{stage}</div>
          )}
        </FocusModal.Body>

        <FocusModal.Footer>
          <div className="flex justify-end gap-2">
            {isError && onRetry && (
              <button
                onClick={onRetry}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Retry
              </button>
            )}

            <button
              onClick={onClose}
              disabled={!isComplete && !isError}
              className={`px-4 py-2 rounded-md transition-colors ${
                isComplete || isError
                  ? "bg-gray-600 text-white hover:bg-gray-700"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              {isComplete || isError ? "Close" : "Cancel"}
            </button>
          </div>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};

// Hook for managing progress state
export const useProgressState = () => {
  const [progressState, setProgressState] = React.useState<ProgressState>({
    operation: "export",
    stage: "Initializing...",
    progress: 0,
    totalItems: 0,
    processedItems: 0,
    errors: [],
    warnings: [],
    isComplete: false,
    isError: false,
  });

  const updateProgress = React.useCallback(
    (updates: Partial<ProgressState>) => {
      setProgressState((prev) => ({ ...prev, ...updates }));
    },
    []
  );

  const resetProgress = React.useCallback((operation: "export" | "import") => {
    setProgressState({
      operation,
      stage: "Initializing...",
      progress: 0,
      totalItems: 0,
      processedItems: 0,
      errors: [],
      warnings: [],
      isComplete: false,
      isError: false,
    });
  }, []);

  const addError = React.useCallback((error: string) => {
    setProgressState((prev) => ({
      ...prev,
      errors: [...prev.errors, error],
    }));
  }, []);

  const addWarning = React.useCallback((warning: string) => {
    setProgressState((prev) => ({
      ...prev,
      warnings: [...prev.warnings, warning],
    }));
  }, []);

  const setComplete = React.useCallback((result?: any) => {
    setProgressState((prev) => ({
      ...prev,
      isComplete: true,
      progress: 100,
      processedItems: prev.totalItems,
      result,
    }));
  }, []);

  const setError = React.useCallback((error: string) => {
    setProgressState((prev) => ({
      ...prev,
      isError: true,
      errors: [...prev.errors, error],
    }));
  }, []);

  return {
    progressState,
    updateProgress,
    resetProgress,
    addError,
    addWarning,
    setComplete,
    setError,
  };
};
