import React, { useState, useEffect } from "react";
import { FocusModal, Button, Checkbox, Input, Label } from "@camped-ai/ui";
import { Download, FileText, Table } from "lucide-react";
import type { ExportConfig } from "./utils/export-utils";

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (config: ExportConfig) => void;
  availableFilters: {
    currencies: string[];
    roomTypes: string[];
    seasonalPeriods: Array<{
      id: string;
      name: string;
      start_date: string;
      end_date: string;
    }>;
  };
  isExporting?: boolean;
}

export const ExportModal: React.FC<ExportModalProps> = ({
  isOpen,
  onClose,
  onExport,
  availableFilters,
  isExporting = false,
}) => {
  const [config, setConfig] = useState<ExportConfig>({
    format: "excel",
    exportType: "detailed",
    includeBasePricing: true,
    includeSeasonalPricing: true,
    includeCostMargin: true,
    filters: {
      currencies: [],
      roomTypes: [],
      seasonalPeriods: [],
      dateRange: undefined,
    },
  });

  const [estimatedRows, setEstimatedRows] = useState(0);

  // Reset config when modal opens
  useEffect(() => {
    if (isOpen) {
      setConfig({
        format: "excel",
        exportType: "detailed",
        includeBasePricing: true,
        includeSeasonalPricing: true,
        includeCostMargin: true,
        filters: {
          currencies: [],
          roomTypes: [],
          seasonalPeriods: [],
          dateRange: undefined,
        },
      });
    }
  }, [isOpen]);

  // Calculate estimated rows based on filters
  useEffect(() => {
    // This is a rough estimation - in real implementation, you'd call an API
    let baseRows = availableFilters.roomTypes.length * 3; // Assume 3 occupancy types per room
    let seasonalRows = availableFilters.seasonalPeriods.length * baseRows;

    let total = 0;
    if (config.includeBasePricing) total += baseRows;
    if (config.includeSeasonalPricing) total += seasonalRows;

    // Apply filters
    if (config.filters.roomTypes.length > 0) {
      total = Math.round(
        total *
          (config.filters.roomTypes.length / availableFilters.roomTypes.length)
      );
    }
    if (config.filters.seasonalPeriods.length > 0) {
      const seasonalRatio =
        config.filters.seasonalPeriods.length /
        availableFilters.seasonalPeriods.length;
      total = Math.round(total * seasonalRatio);
    }

    setEstimatedRows(Math.max(1, total));
  }, [config, availableFilters]);

  const handleExport = () => {
    onExport(config);
  };

  const handleFilterChange = (
    filterType: keyof ExportConfig["filters"],
    value: any
  ) => {
    setConfig((prev) => ({
      ...prev,
      filters: {
        ...prev.filters,
        [filterType]: value,
      },
    }));
  };

  const toggleCurrency = (currency: string) => {
    const current = config.filters.currencies;
    const updated = current.includes(currency)
      ? current.filter((c) => c !== currency)
      : [...current, currency];
    handleFilterChange("currencies", updated);
  };

  const toggleRoomType = (roomType: string) => {
    const current = config.filters.roomTypes;
    const updated = current.includes(roomType)
      ? current.filter((rt) => rt !== roomType)
      : [...current, roomType];
    handleFilterChange("roomTypes", updated);
  };

  const toggleSeasonalPeriod = (periodId: string) => {
    const current = config.filters.seasonalPeriods;
    const updated = current.includes(periodId)
      ? current.filter((sp) => sp !== periodId)
      : [...current, periodId];
    handleFilterChange("seasonalPeriods", updated);
  };

  return (
    <FocusModal open={isOpen} onOpenChange={onClose}>
      <FocusModal.Content className="max-w-2xl">
        <FocusModal.Header>
          <FocusModal.Title className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Pricing Data
          </FocusModal.Title>
        </FocusModal.Header>

        <FocusModal.Body className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Export Format</Label>
            <div className="flex gap-4">
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  name="format"
                  value="excel"
                  checked={config.format === "excel"}
                  onChange={(e) =>
                    setConfig((prev) => ({
                      ...prev,
                      format: e.target.value as "excel",
                    }))
                  }
                  className="text-blue-600"
                />
                <Table className="h-4 w-4" />
                <span className="text-sm">Excel (.xlsx)</span>
              </label>
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  name="format"
                  value="csv"
                  checked={config.format === "csv"}
                  onChange={(e) =>
                    setConfig((prev) => ({
                      ...prev,
                      format: e.target.value as "csv",
                    }))
                  }
                  className="text-blue-600"
                />
                <FileText className="h-4 w-4" />
                <span className="text-sm">CSV</span>
              </label>
            </div>
          </div>

          {/* Export Type */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Export Type</Label>
            <div className="flex gap-4">
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  name="exportType"
                  value="summary"
                  checked={config.exportType === "summary"}
                  onChange={(e) =>
                    setConfig((prev) => ({
                      ...prev,
                      exportType: e.target.value as "summary",
                    }))
                  }
                  className="text-blue-600"
                />
                <span className="text-sm">
                  Summary (Base prices + seasonal periods list)
                </span>
              </label>
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  name="exportType"
                  value="detailed"
                  checked={config.exportType === "detailed"}
                  onChange={(e) =>
                    setConfig((prev) => ({
                      ...prev,
                      exportType: e.target.value as "detailed",
                    }))
                  }
                  className="text-blue-600"
                />
                <span className="text-sm">
                  Detailed (Separate rows for each pricing rule)
                </span>
              </label>
            </div>
          </div>

          {/* Data Inclusion */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Include Data</Label>
            <div className="space-y-2">
              <label className="flex items-center gap-2 cursor-pointer">
                <Checkbox
                  checked={config.includeBasePricing}
                  onCheckedChange={(checked) =>
                    setConfig((prev) => ({
                      ...prev,
                      includeBasePricing: !!checked,
                    }))
                  }
                />
                <span className="text-sm">Base Pricing</span>
              </label>
              <label className="flex items-center gap-2 cursor-pointer">
                <Checkbox
                  checked={config.includeSeasonalPricing}
                  onCheckedChange={(checked) =>
                    setConfig((prev) => ({
                      ...prev,
                      includeSeasonalPricing: !!checked,
                    }))
                  }
                />
                <span className="text-sm">Seasonal Pricing</span>
              </label>
              <label className="flex items-center gap-2 cursor-pointer">
                <Checkbox
                  checked={config.includeCostMargin}
                  onCheckedChange={(checked) =>
                    setConfig((prev) => ({
                      ...prev,
                      includeCostMargin: !!checked,
                    }))
                  }
                />
                <span className="text-sm">Cost & Margin Data</span>
              </label>
            </div>
          </div>

          {/* Filters */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">Filters (Optional)</Label>

            {/* Currency Filter */}
            <div className="space-y-2">
              <Label className="text-xs text-gray-600">Currencies</Label>
              <div className="flex flex-wrap gap-2">
                {availableFilters.currencies.map((currency) => (
                  <label
                    key={currency}
                    className="flex items-center gap-1 cursor-pointer"
                  >
                    <Checkbox
                      checked={config.filters.currencies.includes(currency)}
                      onCheckedChange={() => toggleCurrency(currency)}
                    />
                    <span className="text-sm">{currency}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Room Type Filter */}
            <div className="space-y-2">
              <Label className="text-xs text-gray-600">Room Types</Label>
              <div className="max-h-32 overflow-y-auto space-y-1">
                {availableFilters.roomTypes.map((roomType) => (
                  <label
                    key={roomType}
                    className="flex items-center gap-2 cursor-pointer"
                  >
                    <Checkbox
                      checked={config.filters.roomTypes.includes(roomType)}
                      onCheckedChange={() => toggleRoomType(roomType)}
                    />
                    <span className="text-sm">{roomType}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Seasonal Period Filter */}
            {availableFilters.seasonalPeriods.length > 0 && (
              <div className="space-y-2">
                <Label className="text-xs text-gray-600">
                  Seasonal Periods
                </Label>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {availableFilters.seasonalPeriods.map((period) => (
                    <label
                      key={period.id}
                      className="flex items-center gap-2 cursor-pointer"
                    >
                      <Checkbox
                        checked={config.filters.seasonalPeriods.includes(
                          period.id
                        )}
                        onCheckedChange={() => toggleSeasonalPeriod(period.id)}
                      />
                      <span className="text-sm">
                        {period.name} ({period.start_date} to {period.end_date})
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            )}

            {/* Date Range Filter */}
            <div className="space-y-2">
              <Label className="text-xs text-gray-600">
                Date Range (for seasonal pricing)
              </Label>
              <div className="flex gap-2">
                <Input
                  type="date"
                  placeholder="Start date"
                  value={config.filters.dateRange?.start || ""}
                  onChange={(e) =>
                    handleFilterChange("dateRange", {
                      ...config.filters.dateRange,
                      start: e.target.value,
                    })
                  }
                  className="flex-1"
                />
                <Input
                  type="date"
                  placeholder="End date"
                  value={config.filters.dateRange?.end || ""}
                  onChange={(e) =>
                    handleFilterChange("dateRange", {
                      ...config.filters.dateRange,
                      end: e.target.value,
                    })
                  }
                  className="flex-1"
                />
              </div>
            </div>
          </div>

          {/* Export Preview */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-800 mb-2">Export Preview</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <div>• Estimated rows: {estimatedRows.toLocaleString()}</div>
              <div>• Format: {config.format.toUpperCase()}</div>
              <div>• Type: {config.exportType}</div>
              <div>
                • Includes:{" "}
                {[
                  config.includeBasePricing && "Base Pricing",
                  config.includeSeasonalPricing && "Seasonal Pricing",
                  config.includeCostMargin && "Cost/Margin Data",
                ]
                  .filter(Boolean)
                  .join(", ")}
              </div>
            </div>
          </div>
        </FocusModal.Body>

        <FocusModal.Footer>
          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              onClick={onClose}
              disabled={isExporting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleExport}
              disabled={
                isExporting ||
                (!config.includeBasePricing && !config.includeSeasonalPricing)
              }
              className="flex items-center gap-2"
            >
              {isExporting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  Export Data
                </>
              )}
            </Button>
          </div>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};
