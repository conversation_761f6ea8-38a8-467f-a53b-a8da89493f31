import * as XLSX from 'xlsx';

export interface ParsedData {
  headers: string[];
  rows: any[];
  totalRows: number;
}

export interface ExportOptions {
  filename: string;
  format: 'csv' | 'excel';
  data: any[];
  headers?: string[];
}

/**
 * Utility class for handling CSV and Excel file operations
 */
export class CSVExcelUtils {
  /**
   * Parse uploaded CSV or Excel file
   */
  static async parseFile(file: File): Promise<ParsedData> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = e.target?.result;
          let workbook: XLSX.WorkBook;
          
          if (file.name.endsWith('.csv')) {
            workbook = XLSX.read(data, { type: 'binary' });
          } else {
            workbook = XLSX.read(data, { type: 'array' });
          }
          
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          
          if (jsonData.length === 0) {
            throw new Error('File is empty');
          }
          
          const headers = jsonData[0] as string[];
          const rows = jsonData.slice(1).filter(row => 
            Array.isArray(row) && row.some(cell => cell !== null && cell !== undefined && cell !== '')
          );
          
          resolve({
            headers,
            rows: rows.map(row => {
              const obj: any = {};
              headers.forEach((header, index) => {
                obj[header] = row[index] || '';
              });
              return obj;
            }),
            totalRows: rows.length
          });
        } catch (error) {
          reject(new Error(`Failed to parse file: ${error.message}`));
        }
      };
      
      reader.onerror = () => reject(new Error('Failed to read file'));
      
      if (file.name.endsWith('.csv')) {
        reader.readAsBinaryString(file);
      } else {
        reader.readAsArrayBuffer(file);
      }
    });
  }

  /**
   * Generate CSV content from data
   */
  static generateCSV(data: any[], headers?: string[]): string {
    if (data.length === 0) return '';
    
    const actualHeaders = headers || Object.keys(data[0]);
    const csvHeaders = actualHeaders.join(',');
    
    const csvRows = data.map(row => 
      actualHeaders.map(header => {
        const value = row[header];
        // Handle values that contain commas, quotes, or newlines
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || '';
      }).join(',')
    );
    
    return [csvHeaders, ...csvRows].join('\n');
  }

  /**
   * Generate Excel buffer from data
   */
  static generateExcel(data: any[], headers?: string[]): ArrayBuffer {
    if (data.length === 0) {
      const emptyWorkbook = XLSX.utils.book_new();
      const emptyWorksheet = XLSX.utils.aoa_to_sheet([['No data']]);
      XLSX.utils.book_append_sheet(emptyWorkbook, emptyWorksheet, 'Sheet1');
      return XLSX.write(emptyWorkbook, { bookType: 'xlsx', type: 'array' });
    }
    
    const actualHeaders = headers || Object.keys(data[0]);
    const worksheetData = [
      actualHeaders,
      ...data.map(row => actualHeaders.map(header => row[header] || ''))
    ];
    
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
    
    // Auto-size columns
    const colWidths = actualHeaders.map(header => ({
      wch: Math.max(
        header.length,
        ...data.map(row => String(row[header] || '').length)
      )
    }));
    worksheet['!cols'] = colWidths;
    
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Pricing Data');
    return XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  }

  /**
   * Download file to user's device
   */
  static downloadFile(options: ExportOptions): void {
    let blob: Blob;
    let mimeType: string;
    
    if (options.format === 'csv') {
      const csvContent = this.generateCSV(options.data, options.headers);
      blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      mimeType = 'text/csv';
    } else {
      const excelBuffer = this.generateExcel(options.data, options.headers);
      blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    }
    
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = options.filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    window.URL.revokeObjectURL(url);
  }

  /**
   * Validate file format
   */
  static validateFileFormat(file: File): { valid: boolean; error?: string } {
    const validExtensions = ['.csv', '.xlsx', '.xls'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    
    if (!validExtensions.includes(fileExtension)) {
      return {
        valid: false,
        error: `Invalid file format. Supported formats: ${validExtensions.join(', ')}`
      };
    }
    
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size exceeds 10MB limit'
      };
    }
    
    return { valid: true };
  }

  /**
   * Generate import template
   */
  static generateImportTemplate(templateType: 'base_pricing' | 'seasonal_pricing' | 'both'): any[] {
    const baseHeaders = [
      'room_type',
      'occupancy_type', 
      'meal_plan',
      'currency',
      'price_mon',
      'price_tue',
      'price_wed',
      'price_thu',
      'price_fri',
      'price_sat',
      'price_sun'
    ];
    
    const costMarginHeaders = [
      'default_gross_cost',
      'default_fixed_margin',
      'default_margin_percentage',
      'mon_gross_cost',
      'mon_fixed_margin',
      'mon_margin_percentage',
      'tue_gross_cost',
      'tue_fixed_margin',
      'tue_margin_percentage',
      'wed_gross_cost',
      'wed_fixed_margin',
      'wed_margin_percentage',
      'thu_gross_cost',
      'thu_fixed_margin',
      'thu_margin_percentage',
      'fri_gross_cost',
      'fri_fixed_margin',
      'fri_margin_percentage',
      'sat_gross_cost',
      'sat_fixed_margin',
      'sat_margin_percentage',
      'sun_gross_cost',
      'sun_fixed_margin',
      'sun_margin_percentage'
    ];
    
    const seasonalHeaders = [
      'seasonal_period_name',
      'seasonal_start_date',
      'seasonal_end_date'
    ];
    
    let headers: string[] = [];
    
    if (templateType === 'base_pricing') {
      headers = [...baseHeaders, ...costMarginHeaders];
    } else if (templateType === 'seasonal_pricing') {
      headers = [...baseHeaders, ...costMarginHeaders, ...seasonalHeaders];
    } else {
      headers = [...baseHeaders, ...costMarginHeaders, ...seasonalHeaders];
    }
    
    // Create sample rows
    const sampleRows = [
      {
        room_type: 'Standard Room',
        occupancy_type: 'Single',
        meal_plan: 'Breakfast',
        currency: 'CHF',
        price_mon: 100,
        price_tue: 100,
        price_wed: 100,
        price_thu: 100,
        price_fri: 120,
        price_sat: 150,
        price_sun: 130,
        default_gross_cost: 60,
        default_fixed_margin: 20,
        default_margin_percentage: 0,
        ...(templateType !== 'base_pricing' && {
          seasonal_period_name: 'Summer Season',
          seasonal_start_date: '2024-06-01',
          seasonal_end_date: '2024-08-31'
        })
      }
    ];
    
    return sampleRows;
  }
}
