import { validateSeasonalPeriod } from '../../../../utils/season-validation';
import type { ImportRow, ParsedPricingData, ImportError, ImportWarning } from './import-utils';

export interface ValidationContext {
  existingSeasons: SeasonalPeriod[];
  existingRoomTypes: string[];
  existingOccupancyTypes: string[];
  existingMealPlans: string[];
  supportedCurrencies: string[];
  hotelId: string;
}

export interface SeasonalPeriod {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ImportError[];
  warnings: ImportWarning[];
  duplicateSeasons: DuplicateSeasonInfo[];
}

export interface DuplicateSeasonInfo {
  seasonName: string;
  dateRange: string;
  conflictType: 'duplicate_name' | 'overlapping_dates' | 'exact_match';
  existingSeasonId?: string;
  affectedRows: number[];
}

/**
 * Comprehensive validation utility for import data
 */
export class ImportValidator {
  /**
   * Validate complete import dataset
   */
  static validateImportData(
    importRows: ImportRow[],
    context: ValidationContext
  ): ValidationResult {
    const errors: ImportError[] = [];
    const warnings: ImportWarning[] = [];
    const duplicateSeasons: DuplicateSeasonInfo[] = [];
    
    // Group seasonal data for validation
    const seasonalDataMap = new Map<string, { data: ParsedPricingData; rows: number[] }>();
    
    importRows.forEach(row => {
      if (!row.parsedData || !row.isValid) return;
      
      const data = row.parsedData;
      
      // Validate individual row
      const rowValidation = this.validateRow(data, context, row.rowNumber);
      errors.push(...rowValidation.errors);
      warnings.push(...rowValidation.warnings);
      
      // Collect seasonal data for duplicate checking
      if (data.pricing_type === 'seasonal' && data.seasonal_period) {
        const seasonKey = `${data.seasonal_period.name}_${data.seasonal_period.start_date}_${data.seasonal_period.end_date}`;
        
        if (!seasonalDataMap.has(seasonKey)) {
          seasonalDataMap.set(seasonKey, { data, rows: [] });
        }
        seasonalDataMap.get(seasonKey)!.rows.push(row.rowNumber);
      }
    });
    
    // Validate seasonal periods for duplicates and overlaps
    const seasonalValidation = this.validateSeasonalPeriods(seasonalDataMap, context);
    duplicateSeasons.push(...seasonalValidation.duplicateSeasons);
    errors.push(...seasonalValidation.errors);
    warnings.push(...seasonalValidation.warnings);
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      duplicateSeasons
    };
  }

  /**
   * Validate individual row data
   */
  private static validateRow(
    data: ParsedPricingData,
    context: ValidationContext,
    rowNumber: number
  ): { errors: ImportError[]; warnings: ImportWarning[] } {
    const errors: ImportError[] = [];
    const warnings: ImportWarning[] = [];
    
    // Validate room type reference
    if (!context.existingRoomTypes.includes(data.room_type)) {
      warnings.push({
        type: 'reference_not_found',
        message: `Room type '${data.room_type}' not found in system. It will be created if import proceeds.`,
        rowNumber,
        field: 'room_type'
      });
    }
    
    // Validate occupancy type reference
    if (!context.existingOccupancyTypes.includes(data.occupancy_type)) {
      warnings.push({
        type: 'reference_not_found',
        message: `Occupancy type '${data.occupancy_type}' not found in system. It will be created if import proceeds.`,
        rowNumber,
        field: 'occupancy_type'
      });
    }
    
    // Validate meal plan reference
    if (!context.existingMealPlans.includes(data.meal_plan)) {
      warnings.push({
        type: 'reference_not_found',
        message: `Meal plan '${data.meal_plan}' not found in system. It will be created if import proceeds.`,
        rowNumber,
        field: 'meal_plan'
      });
    }
    
    // Validate currency
    if (!context.supportedCurrencies.includes(data.currency)) {
      errors.push({
        type: 'invalid_data_type',
        message: `Unsupported currency '${data.currency}'. Supported currencies: ${context.supportedCurrencies.join(', ')}`,
        rowNumber,
        field: 'currency'
      });
    }
    
    // Validate pricing data consistency
    this.validatePricingConsistency(data, rowNumber, errors, warnings);
    
    // Validate cost/margin data if present
    if (data.default_values || data.weekday_values) {
      this.validateCostMarginData(data, rowNumber, errors, warnings);
    }
    
    return { errors, warnings };
  }

  /**
   * Validate seasonal periods for duplicates and overlaps
   */
  private static validateSeasonalPeriods(
    seasonalDataMap: Map<string, { data: ParsedPricingData; rows: number[] }>,
    context: ValidationContext
  ): { errors: ImportError[]; warnings: ImportWarning[]; duplicateSeasons: DuplicateSeasonInfo[] } {
    const errors: ImportError[] = [];
    const warnings: ImportWarning[] = [];
    const duplicateSeasons: DuplicateSeasonInfo[] = [];
    
    const importSeasons: SeasonalPeriod[] = [];
    
    // Convert import data to seasonal periods for validation
    seasonalDataMap.forEach(({ data, rows }, seasonKey) => {
      if (data.seasonal_period) {
        importSeasons.push({
          id: `import_${seasonKey}`,
          name: data.seasonal_period.name,
          start_date: data.seasonal_period.start_date,
          end_date: data.seasonal_period.end_date
        });
      }
    });
    
    // Check each import season against existing seasons
    seasonalDataMap.forEach(({ data, rows }, seasonKey) => {
      if (!data.seasonal_period) return;
      
      const seasonData = {
        name: data.seasonal_period.name,
        start_date: data.seasonal_period.start_date,
        end_date: data.seasonal_period.end_date
      };
      
      // Use existing validation logic
      const validationResult = validateSeasonalPeriod(seasonData, context.existingSeasons);
      
      if (!validationResult.isValid) {
        // Determine conflict type
        let conflictType: DuplicateSeasonInfo['conflictType'] = 'duplicate_name';
        let existingSeasonId: string | undefined;
        
        if (validationResult.error.includes('overlaps')) {
          conflictType = 'overlapping_dates';
          // Extract existing season info from error message if possible
          const match = validationResult.error.match(/existing season '([^']+)'/);
          if (match) {
            const existingSeason = context.existingSeasons.find(s => s.name === match[1]);
            existingSeasonId = existingSeason?.id;
          }
        } else if (validationResult.error.includes('already exists')) {
          conflictType = 'duplicate_name';
          const existingSeason = context.existingSeasons.find(
            s => s.name.toLowerCase() === seasonData.name.toLowerCase()
          );
          existingSeasonId = existingSeason?.id;
        }
        
        duplicateSeasons.push({
          seasonName: seasonData.name,
          dateRange: `${seasonData.start_date} to ${seasonData.end_date}`,
          conflictType,
          existingSeasonId,
          affectedRows: rows
        });
        
        // Add errors for affected rows
        rows.forEach(rowNumber => {
          errors.push({
            type: 'invalid_date_range',
            message: validationResult.error,
            rowNumber,
            field: 'seasonal_period'
          });
        });
      }
    });
    
    // Check for duplicates within import data
    const nameGroups = new Map<string, { seasonKey: string; rows: number[] }[]>();
    seasonalDataMap.forEach(({ data, rows }, seasonKey) => {
      if (!data.seasonal_period) return;
      
      const name = data.seasonal_period.name.toLowerCase();
      if (!nameGroups.has(name)) {
        nameGroups.set(name, []);
      }
      nameGroups.get(name)!.push({ seasonKey, rows });
    });
    
    nameGroups.forEach((groups, name) => {
      if (groups.length > 1) {
        const allRows = groups.flatMap(g => g.rows);
        const firstGroup = groups[0];
        const firstData = seasonalDataMap.get(firstGroup.seasonKey)?.data;
        
        if (firstData?.seasonal_period) {
          duplicateSeasons.push({
            seasonName: firstData.seasonal_period.name,
            dateRange: `${firstData.seasonal_period.start_date} to ${firstData.seasonal_period.end_date}`,
            conflictType: 'duplicate_name',
            affectedRows: allRows
          });
        }
        
        // Add warnings for duplicate rows (except the first occurrence)
        groups.slice(1).forEach(group => {
          group.rows.forEach(rowNumber => {
            warnings.push({
              type: 'duplicate_row',
              message: `Duplicate seasonal period '${name}' found in import data`,
              rowNumber
            });
          });
        });
      }
    });
    
    return { errors, warnings, duplicateSeasons };
  }

  /**
   * Validate pricing data consistency
   */
  private static validatePricingConsistency(
    data: ParsedPricingData,
    rowNumber: number,
    errors: ImportError[],
    warnings: ImportWarning[]
  ): void {
    // Check if all weekday prices are zero
    const allPricesZero = Object.values(data.weekday_prices).every(price => price === 0);
    if (allPricesZero) {
      warnings.push({
        type: 'missing_cost_margin',
        message: 'All weekday prices are zero. This may not be intended.',
        rowNumber
      });
    }
    
    // Check for unrealistic price values
    Object.entries(data.weekday_prices).forEach(([day, price]) => {
      if (price > 10000) {
        warnings.push({
          type: 'invalid_data_type',
          message: `${day} price (${price}) seems unusually high. Please verify.`,
          rowNumber
        });
      }
    });
  }

  /**
   * Validate cost/margin data
   */
  private static validateCostMarginData(
    data: ParsedPricingData,
    rowNumber: number,
    errors: ImportError[],
    warnings: ImportWarning[]
  ): void {
    if (!data.default_values && !data.weekday_values) return;
    
    // Validate default values
    if (data.default_values) {
      const { gross_cost, fixed_margin, margin_percentage } = data.default_values;
      
      // Check for mutual exclusion of fixed margin and margin percentage
      if (fixed_margin > 0 && margin_percentage > 0) {
        warnings.push({
          type: 'invalid_data_type',
          message: 'Both fixed margin and margin percentage are specified. Fixed margin will take precedence.',
          rowNumber
        });
      }
      
      // Validate margin percentage range
      if (margin_percentage > 100) {
        warnings.push({
          type: 'invalid_data_type',
          message: `Margin percentage (${margin_percentage}%) seems high. Please verify.`,
          rowNumber
        });
      }
      
      // Check if cost + margin exceeds price
      if (data.weekday_prices) {
        Object.entries(data.weekday_prices).forEach(([day, price]) => {
          const totalCost = gross_cost + (fixed_margin || (gross_cost * margin_percentage / 100));
          if (totalCost > price && price > 0) {
            warnings.push({
              type: 'invalid_data_type',
              message: `${day}: Total cost (${totalCost.toFixed(2)}) exceeds price (${price}). This will result in negative profit.`,
              rowNumber
            });
          }
        });
      }
    }
    
    // Validate weekday-specific values
    if (data.weekday_values) {
      Object.entries(data.weekday_values).forEach(([day, values]) => {
        if (values.fixed_margin > 0 && values.margin_percentage > 0) {
          warnings.push({
            type: 'invalid_data_type',
            message: `${day}: Both fixed margin and margin percentage specified. Fixed margin will take precedence.`,
            rowNumber
          });
        }
      });
    }
  }

  /**
   * Generate validation summary
   */
  static generateValidationSummary(result: ValidationResult): string {
    const parts: string[] = [];
    
    if (result.errors.length > 0) {
      parts.push(`${result.errors.length} error(s) found`);
    }
    
    if (result.warnings.length > 0) {
      parts.push(`${result.warnings.length} warning(s) found`);
    }
    
    if (result.duplicateSeasons.length > 0) {
      parts.push(`${result.duplicateSeasons.length} seasonal period conflict(s) detected`);
    }
    
    if (parts.length === 0) {
      return 'All data validated successfully';
    }
    
    return parts.join(', ');
  }

  /**
   * Check if import can proceed despite warnings
   */
  static canProceedWithWarnings(result: ValidationResult): boolean {
    // Can proceed if there are only warnings, no errors
    return result.errors.length === 0;
  }

  /**
   * Get critical errors that must be fixed before import
   */
  static getCriticalErrors(result: ValidationResult): ImportError[] {
    return result.errors.filter(error => 
      error.type === 'missing_required_field' || 
      error.type === 'invalid_data_type'
    );
  }
}
