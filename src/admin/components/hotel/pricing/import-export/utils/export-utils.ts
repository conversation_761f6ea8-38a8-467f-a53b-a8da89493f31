export interface ExportConfig {
  format: 'csv' | 'excel';
  exportType: 'summary' | 'detailed';
  includeBasePricing: boolean;
  includeSeasonalPricing: boolean;
  includeCostMargin: boolean;
  filters: {
    currencies: string[];
    roomTypes: string[];
    seasonalPeriods: string[];
    dateRange?: {
      start: string;
      end: string;
    };
  };
}

export interface SummaryExportRow {
  room_type: string;
  occupancy_type: string;
  meal_plan: string;
  currency: string;
  base_price_mon: number;
  base_price_tue: number;
  base_price_wed: number;
  base_price_thu: number;
  base_price_fri: number;
  base_price_sat: number;
  base_price_sun: number;
  seasonal_periods: string; // Comma-separated list
  seasonal_count: number;
}

export interface DetailedExportRow {
  room_type: string;
  occupancy_type: string;
  meal_plan: string;
  currency: string;
  pricing_type: 'base' | 'seasonal';
  seasonal_period?: string;
  seasonal_start_date?: string;
  seasonal_end_date?: string;
  // Weekday prices
  price_mon: number;
  price_tue: number;
  price_wed: number;
  price_thu: number;
  price_fri: number;
  price_sat: number;
  price_sun: number;
  // Cost/Margin data (if included)
  default_gross_cost?: number;
  default_fixed_margin?: number;
  default_margin_percentage?: number;
  // Weekday-specific cost/margin
  mon_gross_cost?: number;
  mon_fixed_margin?: number;
  mon_margin_percentage?: number;
  tue_gross_cost?: number;
  tue_fixed_margin?: number;
  tue_margin_percentage?: number;
  wed_gross_cost?: number;
  wed_fixed_margin?: number;
  wed_margin_percentage?: number;
  thu_gross_cost?: number;
  thu_fixed_margin?: number;
  thu_margin_percentage?: number;
  fri_gross_cost?: number;
  fri_fixed_margin?: number;
  fri_margin_percentage?: number;
  sat_gross_cost?: number;
  sat_fixed_margin?: number;
  sat_margin_percentage?: number;
  sun_gross_cost?: number;
  sun_fixed_margin?: number;
  sun_margin_percentage?: number;
}

/**
 * Utility class for formatting pricing data for export
 */
export class PricingExporter {
  /**
   * Format pricing data for summary export
   */
  static formatSummaryData(pricingData: any[], config: ExportConfig): SummaryExportRow[] {
    const summaryMap = new Map<string, SummaryExportRow>();
    
    pricingData.forEach(item => {
      // Apply filters
      if (!this.passesFilters(item, config.filters)) {
        return;
      }
      
      const key = `${item.room_type}_${item.occupancy_type}_${item.meal_plan}_${item.currency}`;
      
      if (!summaryMap.has(key)) {
        summaryMap.set(key, {
          room_type: item.room_type,
          occupancy_type: item.occupancy_type,
          meal_plan: item.meal_plan,
          currency: item.currency,
          base_price_mon: 0,
          base_price_tue: 0,
          base_price_wed: 0,
          base_price_thu: 0,
          base_price_fri: 0,
          base_price_sat: 0,
          base_price_sun: 0,
          seasonal_periods: '',
          seasonal_count: 0
        });
      }
      
      const summary = summaryMap.get(key)!;
      
      if (item.pricing_type === 'base') {
        summary.base_price_mon = item.price_mon || 0;
        summary.base_price_tue = item.price_tue || 0;
        summary.base_price_wed = item.price_wed || 0;
        summary.base_price_thu = item.price_thu || 0;
        summary.base_price_fri = item.price_fri || 0;
        summary.base_price_sat = item.price_sat || 0;
        summary.base_price_sun = item.price_sun || 0;
      } else if (item.pricing_type === 'seasonal' && item.seasonal_period) {
        const periods = summary.seasonal_periods ? summary.seasonal_periods.split(', ') : [];
        if (!periods.includes(item.seasonal_period)) {
          periods.push(item.seasonal_period);
          summary.seasonal_periods = periods.join(', ');
          summary.seasonal_count++;
        }
      }
    });
    
    return Array.from(summaryMap.values());
  }

  /**
   * Format pricing data for detailed export
   */
  static formatDetailedData(pricingData: any[], config: ExportConfig): DetailedExportRow[] {
    return pricingData
      .filter(item => this.passesFilters(item, config.filters))
      .map(item => {
        const baseRow: DetailedExportRow = {
          room_type: item.room_type,
          occupancy_type: item.occupancy_type,
          meal_plan: item.meal_plan,
          currency: item.currency,
          pricing_type: item.pricing_type,
          price_mon: item.price_mon || 0,
          price_tue: item.price_tue || 0,
          price_wed: item.price_wed || 0,
          price_thu: item.price_thu || 0,
          price_fri: item.price_fri || 0,
          price_sat: item.price_sat || 0,
          price_sun: item.price_sun || 0,
        };
        
        // Add seasonal data if applicable
        if (item.pricing_type === 'seasonal') {
          baseRow.seasonal_period = item.seasonal_period;
          baseRow.seasonal_start_date = item.seasonal_start_date;
          baseRow.seasonal_end_date = item.seasonal_end_date;
        }
        
        // Add cost/margin data if included
        if (config.includeCostMargin) {
          baseRow.default_gross_cost = item.default_gross_cost;
          baseRow.default_fixed_margin = item.default_fixed_margin;
          baseRow.default_margin_percentage = item.default_margin_percentage;
          
          // Weekday-specific cost/margin
          const weekdays = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
          weekdays.forEach(day => {
            baseRow[`${day}_gross_cost`] = item[`${day}_gross_cost`];
            baseRow[`${day}_fixed_margin`] = item[`${day}_fixed_margin`];
            baseRow[`${day}_margin_percentage`] = item[`${day}_margin_percentage`];
          });
        }
        
        return baseRow;
      });
  }

  /**
   * Apply filters to pricing data
   */
  private static passesFilters(item: any, filters: ExportConfig['filters']): boolean {
    // Currency filter
    if (filters.currencies.length > 0 && !filters.currencies.includes(item.currency)) {
      return false;
    }
    
    // Room type filter
    if (filters.roomTypes.length > 0 && !filters.roomTypes.includes(item.room_type)) {
      return false;
    }
    
    // Seasonal period filter
    if (filters.seasonalPeriods.length > 0) {
      if (item.pricing_type === 'seasonal' && !filters.seasonalPeriods.includes(item.seasonal_period)) {
        return false;
      }
    }
    
    // Date range filter
    if (filters.dateRange && item.pricing_type === 'seasonal') {
      const itemStartDate = new Date(item.seasonal_start_date);
      const itemEndDate = new Date(item.seasonal_end_date);
      const filterStartDate = new Date(filters.dateRange.start);
      const filterEndDate = new Date(filters.dateRange.end);
      
      // Check if seasonal period overlaps with filter date range
      const hasOverlap = (
        (itemStartDate <= filterEndDate && itemStartDate >= filterStartDate) ||
        (itemEndDate <= filterEndDate && itemEndDate >= filterStartDate) ||
        (itemStartDate <= filterStartDate && itemEndDate >= filterEndDate)
      );
      
      if (!hasOverlap) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Generate filename for export
   */
  static generateFilename(config: ExportConfig, hotelName?: string): string {
    const timestamp = new Date().toISOString().split('T')[0];
    const hotelPrefix = hotelName ? `${hotelName.replace(/[^a-zA-Z0-9]/g, '_')}_` : '';
    const typePrefix = config.exportType === 'summary' ? 'Summary' : 'Detailed';
    const extension = config.format === 'csv' ? 'csv' : 'xlsx';
    
    return `${hotelPrefix}Pricing_${typePrefix}_${timestamp}.${extension}`;
  }

  /**
   * Get export headers based on configuration
   */
  static getExportHeaders(config: ExportConfig): string[] {
    const baseHeaders = [
      'room_type',
      'occupancy_type',
      'meal_plan',
      'currency'
    ];
    
    if (config.exportType === 'summary') {
      return [
        ...baseHeaders,
        'base_price_mon',
        'base_price_tue',
        'base_price_wed',
        'base_price_thu',
        'base_price_fri',
        'base_price_sat',
        'base_price_sun',
        'seasonal_periods',
        'seasonal_count'
      ];
    } else {
      const detailedHeaders = [
        ...baseHeaders,
        'pricing_type',
        'seasonal_period',
        'seasonal_start_date',
        'seasonal_end_date',
        'price_mon',
        'price_tue',
        'price_wed',
        'price_thu',
        'price_fri',
        'price_sat',
        'price_sun'
      ];
      
      if (config.includeCostMargin) {
        detailedHeaders.push(
          'default_gross_cost',
          'default_fixed_margin',
          'default_margin_percentage'
        );
        
        const weekdays = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
        weekdays.forEach(day => {
          detailedHeaders.push(
            `${day}_gross_cost`,
            `${day}_fixed_margin`,
            `${day}_margin_percentage`
          );
        });
      }
      
      return detailedHeaders;
    }
  }

  /**
   * Validate export configuration
   */
  static validateConfig(config: ExportConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!config.includeBasePricing && !config.includeSeasonalPricing) {
      errors.push('At least one pricing type must be included');
    }
    
    if (config.filters.dateRange) {
      const startDate = new Date(config.filters.dateRange.start);
      const endDate = new Date(config.filters.dateRange.end);
      
      if (startDate >= endDate) {
        errors.push('End date must be after start date');
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}
