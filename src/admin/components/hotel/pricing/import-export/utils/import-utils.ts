export interface ImportConfig {
  file: File;
  importType: 'base_pricing' | 'seasonal_pricing' | 'both';
  updateMode: 'create_only' | 'update_existing' | 'create_and_update';
  currency: string;
  validateDuplicates: boolean;
}

export interface ImportRow {
  rowNumber: number;
  data: any;
  isValid: boolean;
  errors: string[];
  warnings: string[];
  parsedData?: ParsedPricingData;
}

export interface ParsedPricingData {
  room_type: string;
  occupancy_type: string;
  meal_plan: string;
  currency: string;
  pricing_type: 'base' | 'seasonal';
  
  // Price data
  weekday_prices: {
    mon: number;
    tue: number;
    wed: number;
    thu: number;
    fri: number;
    sat: number;
    sun: number;
  };
  
  // Cost/margin data (optional)
  default_values?: {
    gross_cost: number;
    fixed_margin: number;
    margin_percentage: number;
  };
  
  weekday_values?: {
    [key: string]: {
      gross_cost: number;
      fixed_margin: number;
      margin_percentage: number;
    };
  };
  
  // Seasonal data (if applicable)
  seasonal_period?: {
    name: string;
    start_date: string;
    end_date: string;
  };
}

export interface ImportPreview {
  totalRows: number;
  validRows: number;
  invalidRows: number;
  warnings: ImportWarning[];
  errors: ImportError[];
  preview: ImportRow[];
  duplicateSeasons: DuplicateSeasonWarning[];
  summary: ImportSummary;
}

export interface ImportWarning {
  type: 'missing_cost_margin' | 'currency_mismatch' | 'date_format' | 'duplicate_row';
  message: string;
  rowNumber?: number;
  field?: string;
}

export interface ImportError {
  type: 'missing_required_field' | 'invalid_data_type' | 'invalid_date_range' | 'reference_not_found';
  message: string;
  rowNumber: number;
  field?: string;
}

export interface DuplicateSeasonWarning {
  seasonName: string;
  dateRange: string;
  affectedRows: number[];
  existingSeasonId?: string;
}

export interface ImportSummary {
  basePricingRows: number;
  seasonalPricingRows: number;
  uniqueRoomTypes: string[];
  uniqueOccupancyTypes: string[];
  uniqueMealPlans: string[];
  uniqueSeasons: string[];
  currencies: string[];
}

/**
 * Utility class for parsing and validating import data
 */
export class ImportDataParser {
  private static readonly REQUIRED_BASE_FIELDS = [
    'room_type',
    'occupancy_type',
    'meal_plan',
    'currency'
  ];
  
  private static readonly WEEKDAY_FIELDS = [
    'price_mon',
    'price_tue', 
    'price_wed',
    'price_thu',
    'price_fri',
    'price_sat',
    'price_sun'
  ];
  
  private static readonly SEASONAL_FIELDS = [
    'seasonal_period_name',
    'seasonal_start_date',
    'seasonal_end_date'
  ];

  /**
   * Parse and validate import data
   */
  static parseImportData(
    rawData: any[], 
    config: ImportConfig,
    existingData?: any[]
  ): ImportPreview {
    const importRows: ImportRow[] = [];
    const warnings: ImportWarning[] = [];
    const errors: ImportError[] = [];
    const duplicateSeasons: DuplicateSeasonWarning[] = [];
    
    let validRows = 0;
    let invalidRows = 0;
    
    // Track seasons for duplicate detection
    const seasonMap = new Map<string, number[]>();
    
    rawData.forEach((row, index) => {
      const rowNumber = index + 2; // +2 because index is 0-based and we skip header row
      const importRow: ImportRow = {
        rowNumber,
        data: row,
        isValid: true,
        errors: [],
        warnings: []
      };
      
      try {
        // Parse the row data
        const parsedData = this.parseRowData(row, config, rowNumber);
        importRow.parsedData = parsedData;
        
        // Validate required fields
        this.validateRequiredFields(parsedData, importRow, config);
        
        // Validate data types and formats
        this.validateDataTypes(parsedData, importRow);
        
        // Validate seasonal data if present
        if (parsedData.seasonal_period) {
          this.validateSeasonalData(parsedData, importRow);
          
          // Track for duplicate detection
          const seasonKey = `${parsedData.seasonal_period.name}_${parsedData.seasonal_period.start_date}_${parsedData.seasonal_period.end_date}`;
          if (!seasonMap.has(seasonKey)) {
            seasonMap.set(seasonKey, []);
          }
          seasonMap.get(seasonKey)!.push(rowNumber);
        }
        
        // Check for currency consistency
        if (config.currency && parsedData.currency !== config.currency) {
          importRow.warnings.push(`Currency mismatch: expected ${config.currency}, found ${parsedData.currency}`);
        }
        
        if (importRow.errors.length === 0) {
          validRows++;
        } else {
          invalidRows++;
          importRow.isValid = false;
        }
        
      } catch (error) {
        importRow.errors.push(`Failed to parse row: ${error.message}`);
        importRow.isValid = false;
        invalidRows++;
      }
      
      importRows.push(importRow);
    });
    
    // Check for duplicate seasons
    if (config.validateDuplicates) {
      seasonMap.forEach((rowNumbers, seasonKey) => {
        if (rowNumbers.length > 1) {
          const [name, startDate, endDate] = seasonKey.split('_');
          duplicateSeasons.push({
            seasonName: name,
            dateRange: `${startDate} to ${endDate}`,
            affectedRows: rowNumbers
          });
        }
      });
    }
    
    // Generate summary
    const summary = this.generateSummary(importRows.filter(row => row.isValid));
    
    return {
      totalRows: rawData.length,
      validRows,
      invalidRows,
      warnings,
      errors,
      preview: importRows.slice(0, 10), // Show first 10 rows for preview
      duplicateSeasons,
      summary
    };
  }

  /**
   * Parse individual row data
   */
  private static parseRowData(row: any, config: ImportConfig, rowNumber: number): ParsedPricingData {
    // Determine pricing type
    const hasSeasonalData = row.seasonal_period_name || row.seasonal_start_date || row.seasonal_end_date;
    const pricingType = hasSeasonalData ? 'seasonal' : 'base';
    
    // Parse weekday prices
    const weekday_prices = {
      mon: this.parseNumber(row.price_mon) || 0,
      tue: this.parseNumber(row.price_tue) || 0,
      wed: this.parseNumber(row.price_wed) || 0,
      thu: this.parseNumber(row.price_thu) || 0,
      fri: this.parseNumber(row.price_fri) || 0,
      sat: this.parseNumber(row.price_sat) || 0,
      sun: this.parseNumber(row.price_sun) || 0,
    };
    
    const parsedData: ParsedPricingData = {
      room_type: String(row.room_type || '').trim(),
      occupancy_type: String(row.occupancy_type || '').trim(),
      meal_plan: String(row.meal_plan || '').trim(),
      currency: String(row.currency || '').trim().toUpperCase(),
      pricing_type: pricingType,
      weekday_prices
    };
    
    // Parse cost/margin data if present
    if (this.hasAnyCostMarginData(row)) {
      parsedData.default_values = {
        gross_cost: this.parseNumber(row.default_gross_cost) || 0,
        fixed_margin: this.parseNumber(row.default_fixed_margin) || 0,
        margin_percentage: this.parseNumber(row.default_margin_percentage) || 0,
      };
      
      parsedData.weekday_values = {};
      const weekdays = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
      weekdays.forEach(day => {
        parsedData.weekday_values![day] = {
          gross_cost: this.parseNumber(row[`${day}_gross_cost`]) || 0,
          fixed_margin: this.parseNumber(row[`${day}_fixed_margin`]) || 0,
          margin_percentage: this.parseNumber(row[`${day}_margin_percentage`]) || 0,
        };
      });
    }
    
    // Parse seasonal data if present
    if (hasSeasonalData) {
      parsedData.seasonal_period = {
        name: String(row.seasonal_period_name || '').trim(),
        start_date: this.parseDate(row.seasonal_start_date),
        end_date: this.parseDate(row.seasonal_end_date),
      };
    }
    
    return parsedData;
  }

  /**
   * Validate required fields
   */
  private static validateRequiredFields(data: ParsedPricingData, importRow: ImportRow, config: ImportConfig): void {
    this.REQUIRED_BASE_FIELDS.forEach(field => {
      if (!data[field]) {
        importRow.errors.push(`Missing required field: ${field}`);
      }
    });
    
    // Check if at least one weekday price is provided
    const hasAnyPrice = Object.values(data.weekday_prices).some(price => price > 0);
    if (!hasAnyPrice) {
      importRow.errors.push('At least one weekday price must be provided');
    }
    
    // Validate seasonal fields if seasonal data is present
    if (data.pricing_type === 'seasonal' && data.seasonal_period) {
      if (!data.seasonal_period.name) {
        importRow.errors.push('Missing seasonal period name');
      }
      if (!data.seasonal_period.start_date) {
        importRow.errors.push('Missing seasonal start date');
      }
      if (!data.seasonal_period.end_date) {
        importRow.errors.push('Missing seasonal end date');
      }
    }
  }

  /**
   * Validate data types and formats
   */
  private static validateDataTypes(data: ParsedPricingData, importRow: ImportRow): void {
    // Validate currency format
    if (data.currency && !/^[A-Z]{3}$/.test(data.currency)) {
      importRow.errors.push('Currency must be a 3-letter code (e.g., USD, EUR, CHF)');
    }
    
    // Validate price values
    Object.entries(data.weekday_prices).forEach(([day, price]) => {
      if (price < 0) {
        importRow.errors.push(`${day} price cannot be negative`);
      }
    });
    
    // Validate cost/margin values if present
    if (data.default_values) {
      if (data.default_values.gross_cost < 0) {
        importRow.errors.push('Default gross cost cannot be negative');
      }
      if (data.default_values.margin_percentage < 0 || data.default_values.margin_percentage > 1000) {
        importRow.errors.push('Default margin percentage must be between 0 and 1000');
      }
    }
  }

  /**
   * Validate seasonal data
   */
  private static validateSeasonalData(data: ParsedPricingData, importRow: ImportRow): void {
    if (!data.seasonal_period) return;
    
    const startDate = new Date(data.seasonal_period.start_date);
    const endDate = new Date(data.seasonal_period.end_date);
    
    if (isNaN(startDate.getTime())) {
      importRow.errors.push('Invalid seasonal start date format');
    }
    
    if (isNaN(endDate.getTime())) {
      importRow.errors.push('Invalid seasonal end date format');
    }
    
    if (startDate >= endDate) {
      importRow.errors.push('Seasonal end date must be after start date');
    }
  }

  /**
   * Generate import summary
   */
  private static generateSummary(validRows: ImportRow[]): ImportSummary {
    const summary: ImportSummary = {
      basePricingRows: 0,
      seasonalPricingRows: 0,
      uniqueRoomTypes: [],
      uniqueOccupancyTypes: [],
      uniqueMealPlans: [],
      uniqueSeasons: [],
      currencies: []
    };
    
    const roomTypeSet = new Set<string>();
    const occupancyTypeSet = new Set<string>();
    const mealPlanSet = new Set<string>();
    const seasonSet = new Set<string>();
    const currencySet = new Set<string>();
    
    validRows.forEach(row => {
      if (!row.parsedData) return;
      
      const data = row.parsedData;
      
      if (data.pricing_type === 'base') {
        summary.basePricingRows++;
      } else {
        summary.seasonalPricingRows++;
        if (data.seasonal_period) {
          seasonSet.add(data.seasonal_period.name);
        }
      }
      
      roomTypeSet.add(data.room_type);
      occupancyTypeSet.add(data.occupancy_type);
      mealPlanSet.add(data.meal_plan);
      currencySet.add(data.currency);
    });
    
    summary.uniqueRoomTypes = Array.from(roomTypeSet);
    summary.uniqueOccupancyTypes = Array.from(occupancyTypeSet);
    summary.uniqueMealPlans = Array.from(mealPlanSet);
    summary.uniqueSeasons = Array.from(seasonSet);
    summary.currencies = Array.from(currencySet);
    
    return summary;
  }

  /**
   * Helper methods
   */
  private static parseNumber(value: any): number | null {
    if (value === null || value === undefined || value === '') return null;
    const num = Number(value);
    return isNaN(num) ? null : num;
  }

  private static parseDate(value: any): string {
    if (!value) return '';
    
    // Try to parse various date formats
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return String(value); // Return as-is if can't parse
    }
    
    return date.toISOString().split('T')[0]; // Return YYYY-MM-DD format
  }

  private static hasAnyCostMarginData(row: any): boolean {
    const costMarginFields = [
      'default_gross_cost', 'default_fixed_margin', 'default_margin_percentage',
      'mon_gross_cost', 'tue_gross_cost', 'wed_gross_cost', 'thu_gross_cost',
      'fri_gross_cost', 'sat_gross_cost', 'sun_gross_cost'
    ];
    
    return costMarginFields.some(field => 
      row[field] !== null && row[field] !== undefined && row[field] !== ''
    );
  }
}
