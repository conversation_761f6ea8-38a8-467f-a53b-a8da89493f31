import React, { useState, useCallback } from "react";
import { FocusModal, Button, Select, Label } from "@camped-ai/ui";
import { Upload, FileText, AlertCircle, Download } from "lucide-react";
import { CSVExcelUtils } from "./utils/csv-excel-utils";
import type { ImportConfig } from "./utils/import-utils";

interface ImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (config: ImportConfig) => void;
  onPreview: (config: ImportConfig) => void;
  availableCurrencies: string[];
  isProcessing?: boolean;
}

export const ImportModal: React.FC<ImportModalProps> = ({
  isOpen,
  onClose,
  onImport,
  onPreview,
  availableCurrencies,
  isProcessing = false,
}) => {
  const [config, setConfig] = useState<ImportConfig>({
    file: null as any,
    importType: "both",
    updateMode: "create_and_update",
    currency: availableCurrencies[0] || "CHF",
    validateDuplicates: true,
  });

  const [dragActive, setDragActive] = useState(false);
  const [fileError, setFileError] = useState<string | null>(null);
  const [fileInfo, setFileInfo] = useState<{
    name: string;
    size: string;
    rows?: number;
  } | null>(null);

  // Reset state when modal opens
  React.useEffect(() => {
    if (isOpen) {
      setConfig({
        file: null as any,
        importType: "both",
        updateMode: "create_and_update",
        currency: availableCurrencies[0] || "CHF",
        validateDuplicates: true,
      });
      setFileError(null);
      setFileInfo(null);
    }
  }, [isOpen, availableCurrencies]);

  const handleFileSelect = useCallback(async (file: File) => {
    setFileError(null);
    setFileInfo(null);

    // Validate file format
    const validation = CSVExcelUtils.validateFileFormat(file);
    if (!validation.valid) {
      setFileError(validation.error!);
      return;
    }

    try {
      // Parse file to get basic info
      const parsedData = await CSVExcelUtils.parseFile(file);

      setConfig((prev) => ({ ...prev, file }));
      setFileInfo({
        name: file.name,
        size:
          file.size < 1024
            ? `${file.size} B`
            : file.size < 1024 * 1024
            ? `${(file.size / 1024).toFixed(1)} KB`
            : `${(file.size / (1024 * 1024)).toFixed(1)} MB`,
        rows: parsedData.totalRows,
      });
    } catch (error) {
      setFileError(
        `Failed to parse file: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setDragActive(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  }, []);

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const downloadTemplate = (
    templateType: "base_pricing" | "seasonal_pricing" | "both"
  ) => {
    const templateData = CSVExcelUtils.generateImportTemplate(templateType);
    CSVExcelUtils.downloadFile({
      filename: `pricing_import_template_${templateType}.xlsx`,
      format: "excel",
      data: templateData,
    });
  };

  const handlePreview = () => {
    if (config.file) {
      onPreview(config);
    }
  };

  const handleImport = () => {
    if (config.file) {
      onImport(config);
    }
  };

  const canProceed = config.file && !fileError && !isProcessing;

  return (
    <FocusModal open={isOpen} onOpenChange={onClose}>
      <FocusModal.Content className="max-w-2xl">
        <FocusModal.Header>
          <FocusModal.Title className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Import Pricing Data
          </FocusModal.Title>
        </FocusModal.Header>

        <FocusModal.Body className="space-y-6">
          {/* File Upload Section */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Upload File</Label>

            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                dragActive
                  ? "border-blue-500 bg-blue-50"
                  : fileError
                  ? "border-red-300 bg-red-50"
                  : "border-gray-300 hover:border-gray-400"
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              {fileInfo ? (
                <div className="space-y-2">
                  <FileText className="h-8 w-8 text-green-600 mx-auto" />
                  <div className="text-sm font-medium text-green-800">
                    {fileInfo.name}
                  </div>
                  <div className="text-xs text-gray-600">
                    {fileInfo.size} • {fileInfo.rows} rows
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="h-8 w-8 text-gray-400 mx-auto" />
                  <div className="text-sm text-gray-600">
                    Drag and drop your file here, or{" "}
                    <label className="text-blue-600 hover:text-blue-700 cursor-pointer underline">
                      browse
                      <input
                        type="file"
                        accept=".csv,.xlsx,.xls"
                        onChange={handleFileInputChange}
                        className="hidden"
                      />
                    </label>
                  </div>
                  <div className="text-xs text-gray-500">
                    Supports CSV, Excel (.xlsx, .xls) • Max 10MB
                  </div>
                </div>
              )}
            </div>

            {fileError && (
              <div className="flex items-center gap-2 text-red-600 text-sm">
                <AlertCircle className="h-4 w-4" />
                {fileError}
              </div>
            )}
          </div>

          {/* Template Download */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">Need a template?</h4>
            <p className="text-sm text-blue-700 mb-3">
              Download a template file with the correct format and sample data.
            </p>
            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => downloadTemplate("base_pricing")}
                className="flex items-center gap-1"
              >
                <Download className="h-3 w-3" />
                Base Pricing
              </Button>
              <Button
                variant="secondary"
                size="small"
                onClick={() => downloadTemplate("seasonal_pricing")}
                className="flex items-center gap-1"
              >
                <Download className="h-3 w-3" />
                Seasonal Pricing
              </Button>
              <Button
                variant="secondary"
                size="small"
                onClick={() => downloadTemplate("both")}
                className="flex items-center gap-1"
              >
                <Download className="h-3 w-3" />
                Complete Template
              </Button>
            </div>
          </div>

          {/* Import Configuration */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">Import Configuration</Label>

            {/* Import Type */}
            <div className="space-y-2">
              <Label className="text-xs text-gray-600">Import Type</Label>
              <Select
                value={config.importType}
                onValueChange={(value: any) =>
                  setConfig((prev) => ({ ...prev, importType: value }))
                }
              >
                <Select.Trigger>
                  <Select.Value />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="base_pricing">
                    Base Pricing Only
                  </Select.Item>
                  <Select.Item value="seasonal_pricing">
                    Seasonal Pricing Only
                  </Select.Item>
                  <Select.Item value="both">
                    Both Base and Seasonal Pricing
                  </Select.Item>
                </Select.Content>
              </Select>
            </div>

            {/* Update Mode */}
            <div className="space-y-2">
              <Label className="text-xs text-gray-600">Update Mode</Label>
              <Select
                value={config.updateMode}
                onValueChange={(value: any) =>
                  setConfig((prev) => ({ ...prev, updateMode: value }))
                }
              >
                <Select.Trigger>
                  <Select.Value />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="create_only">
                    Create New Records Only
                  </Select.Item>
                  <Select.Item value="update_existing">
                    Update Existing Records Only
                  </Select.Item>
                  <Select.Item value="create_and_update">
                    Create New & Update Existing
                  </Select.Item>
                </Select.Content>
              </Select>
            </div>

            {/* Currency */}
            <div className="space-y-2">
              <Label className="text-xs text-gray-600">Target Currency</Label>
              <Select
                value={config.currency}
                onValueChange={(value: string) =>
                  setConfig((prev) => ({ ...prev, currency: value }))
                }
              >
                <Select.Trigger>
                  <Select.Value />
                </Select.Trigger>
                <Select.Content>
                  {availableCurrencies.map((currency) => (
                    <Select.Item key={currency} value={currency}>
                      {currency}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>

            {/* Validation Options */}
            <div className="space-y-2">
              <Label className="text-xs text-gray-600">
                Validation Options
              </Label>
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={config.validateDuplicates}
                  onChange={(e) =>
                    setConfig((prev) => ({
                      ...prev,
                      validateDuplicates: e.target.checked,
                    }))
                  }
                  className="rounded border-gray-300"
                />
                <span className="text-sm">
                  Validate for duplicate seasonal periods
                </span>
              </label>
            </div>
          </div>

          {/* Import Info */}
          {fileInfo && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-800 mb-2">Import Preview</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div>• File: {fileInfo.name}</div>
                <div>• Rows to process: {fileInfo.rows}</div>
                <div>• Import type: {config.importType.replace("_", " ")}</div>
                <div>• Update mode: {config.updateMode.replace(/_/g, " ")}</div>
                <div>• Target currency: {config.currency}</div>
              </div>
            </div>
          )}
        </FocusModal.Body>

        <FocusModal.Footer>
          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              onClick={onClose}
              disabled={isProcessing}
            >
              Cancel
            </Button>

            <Button
              variant="secondary"
              onClick={handlePreview}
              disabled={!canProceed}
              className="flex items-center gap-2"
            >
              Preview & Validate
            </Button>

            <Button
              onClick={handleImport}
              disabled={!canProceed}
              className="flex items-center gap-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Importing...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  Import Data
                </>
              )}
            </Button>
          </div>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};
