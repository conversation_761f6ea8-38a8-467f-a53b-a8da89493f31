
import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
// Import configuration interface
interface ImportConfig {
  importType: 'base_pricing' | 'seasonal_pricing' | 'both';
  updateMode: 'create_only' | 'update_existing' | 'create_and_update';
  currency: string;
  validateDuplicates: boolean;
}

// Import preview interface
interface ImportPreview {
  totalRows: number;
  validRows: number;
  invalidRows: number;
  warnings: ImportWarning[];
  errors: ImportError[];
  preview: ImportRow[];
  duplicateSeasons: DuplicateSeasonWarning[];
  summary: ImportSummary;
}

interface ImportWarning {
  type: string;
  message: string;
  rowNumber?: number;
  field?: string;
}

interface ImportError {
  type: string;
  message: string;
  rowNumber: number;
  field?: string;
}

interface ImportRow {
  rowNumber: number;
  data: any;
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

interface DuplicateSeasonWarning {
  seasonName: string;
  dateRange: string;
  affectedRows: number[];
  existingSeasonId?: string;
}

interface ImportSummary {
  basePricingRows: number;
  seasonalPricingRows: number;
  uniqueRoomTypes: string[];
  uniqueOccupancyTypes: string[];
  uniqueMealPlans: string[];
  uniqueSeasons: string[];
  currencies: string[];
}

// Validation schema for import preview request
export const PostAdminImportPreview = z.object({
  importType: z.enum(['base_pricing', 'seasonal_pricing', 'both']),
  updateMode: z.enum(['create_only', 'update_existing', 'create_and_update']),
  currency: z.string(),
  validateDuplicates: z.boolean(),
  fileData: z.object({
    headers: z.array(z.string()),
    rows: z.array(z.any()),
    totalRows: z.number()
  })
});

export const POST = async (req: MedusaRequest<z.infer<typeof PostAdminImportPreview>>, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    if (!hotelId) {
      return res.status(400).json({
        message: "Hotel ID is required"
      });
    }

    // Validate request body
    const validationResult = PostAdminImportPreview.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid import configuration",
        errors: validationResult.error.errors
      });
    }

    const { importType, updateMode, currency, fileData } = validationResult.data;

    console.log(`Previewing import for hotel: ${hotelId}`);
    console.log(`Import type: ${importType}, Update mode: ${updateMode}`);
    console.log(`File data: ${fileData.totalRows} rows`);

    // Simplified preview implementation
    const preview: ImportPreview = {
      totalRows: fileData.totalRows,
      validRows: Math.max(0, fileData.totalRows - 1), // Assume most rows are valid for preview
      invalidRows: Math.min(1, fileData.totalRows), // Assume minimal invalid rows
      warnings: [],
      errors: [],
      preview: fileData.rows.slice(0, 10).map((row, index) => ({
        rowNumber: index + 2,
        data: row,
        isValid: true,
        errors: [],
        warnings: []
      })),
      duplicateSeasons: [],
      summary: {
        basePricingRows: importType === 'seasonal_pricing' ? 0 : fileData.totalRows,
        seasonalPricingRows: importType === 'base_pricing' ? 0 : fileData.totalRows,
        uniqueRoomTypes: ['Standard Room', 'Deluxe Room'], // Placeholder
        uniqueOccupancyTypes: ['Single', 'Double'], // Placeholder
        uniqueMealPlans: ['Breakfast', 'Half Board'], // Placeholder
        uniqueSeasons: ['Summer Season'], // Placeholder
        currencies: [currency]
      }
    };

    // Basic validation - check for required headers
    const requiredHeaders = ['room_type', 'occupancy_type', 'meal_plan', 'currency'];
    const missingHeaders = requiredHeaders.filter(header =>
      !fileData.headers.some(h => h.toLowerCase().includes(header.toLowerCase()))
    );

    if (missingHeaders.length > 0) {
      preview.errors.push({
        type: 'missing_required_field',
        message: `Missing required columns: ${missingHeaders.join(', ')}`,
        rowNumber: 1,
        field: missingHeaders[0]
      });
      preview.validRows = 0;
      preview.invalidRows = fileData.totalRows;
    }

    // Check for price columns
    const priceHeaders = ['price_mon', 'price_tue', 'price_wed', 'price_thu', 'price_fri', 'price_sat', 'price_sun'];
    const hasPriceColumns = priceHeaders.some(header =>
      fileData.headers.some(h => h.toLowerCase().includes(header.toLowerCase()))
    );

    if (!hasPriceColumns) {
      preview.warnings.push({
        type: 'missing_cost_margin',
        message: 'No weekday price columns found. Please ensure your file includes pricing data.',
        rowNumber: 1
      });
    }

    console.log(`Preview complete: ${preview.validRows} valid, ${preview.invalidRows} invalid, ${preview.warnings.length} warnings`);

    return res.json({
      success: true,
      preview,
      canProceed: preview.validRows > 0 && preview.errors.length === 0,
      validationSummary: preview.errors.length === 0 ? 'All data validated successfully' : `${preview.errors.length} error(s) found`
    });

  } catch (error) {
    console.error("Import preview error:", error);
    return res.status(500).json({
      message: "Failed to preview import data",
      error: (error as any).message
    });
  }
};
