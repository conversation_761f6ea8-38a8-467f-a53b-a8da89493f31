import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
// Export configuration interface
interface ExportConfig {
  format: 'csv' | 'excel';
  exportType: 'summary' | 'detailed';
  includeBasePricing: boolean;
  includeSeasonalPricing: boolean;
  includeCostMargin: boolean;
  filters: {
    currencies: string[];
    roomTypes: string[];
    seasonalPeriods: string[];
    dateRange?: {
      start: string;
      end: string;
    };
  };
}

// Validation schema for export request
export const PostAdminExportPricing = z.object({
  format: z.enum(['csv', 'excel']),
  exportType: z.enum(['summary', 'detailed']),
  includeBasePricing: z.boolean(),
  includeSeasonalPricing: z.boolean(),
  includeCostMargin: z.boolean(),
  filters: z.object({
    currencies: z.array(z.string()).default([]),
    roomTypes: z.array(z.string()).default([]),
    seasonalPeriods: z.array(z.string()).default([]),
    dateRange: z.object({
      start: z.string(),
      end: z.string()
    }).optional()
  }).default({
    currencies: [],
    roomTypes: [],
    seasonalPeriods: []
  })
});

export const POST = async (req: MedusaRequest<z.infer<typeof PostAdminExportPricing>>, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    
    if (!hotelId) {
      return res.status(400).json({
        message: "Hotel ID is required"
      });
    }

    // Validate request body
    const validationResult = PostAdminExportPricing.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid export configuration",
        errors: validationResult.error.errors
      });
    }

    const config = validationResult.data as ExportConfig;

    console.log(`Exporting pricing data for hotel: ${hotelId}`);
    console.log(`Export config:`, config);

    // Get services
    const hotelPricingService = req.scope.resolve("hotelPricingService");
    const hotelService = req.scope.resolve("hotelService");

    // Get hotel information for filename
    let hotelName = 'Hotel';
    try {
      // Try to get hotel name - this may not be available depending on service implementation
      const hotel = await (hotelService as any).retrieve?.(hotelId);
      hotelName = hotel?.name || 'Hotel';
    } catch (error) {
      console.warn("Could not retrieve hotel name:", (error as any).message);
    }

    // Simplified export implementation
    console.log("Fetching pricing data...");

    try {
      const basePriceRules = await hotelPricingService.listBasePriceRules({
        hotel_id: hotelId,
      });

      console.log(`Found ${basePriceRules.length} base price rules`);

      if (basePriceRules.length === 0) {
        return res.status(404).json({
          message: "No pricing data found for this hotel"
        });
      }

      // Create simplified export data
      const exportData = basePriceRules.map((rule: any) => ({
        rule_id: rule.id,
        hotel_id: rule.hotel_id,
        room_config_id: rule.room_config_id,
        occupancy_type_id: rule.occupancy_type_id,
        meal_plan_id: rule.meal_plan_id,
        currency: rule.currency_code,
        description: rule.description || '',
        amount: rule.amount / 100, // Convert from cents
        monday_price: rule.monday_price / 100,
        tuesday_price: rule.tuesday_price / 100,
        wednesday_price: rule.wednesday_price / 100,
        thursday_price: rule.thursday_price / 100,
        friday_price: rule.friday_price / 100,
        saturday_price: rule.saturday_price / 100,
        sunday_price: rule.sunday_price / 100,
        created_at: rule.created_at,
        updated_at: rule.updated_at
      }));

      // Generate CSV content
      const headers = [
        'rule_id', 'hotel_id', 'room_config_id', 'occupancy_type_id', 'meal_plan_id',
        'currency', 'description', 'amount', 'monday_price', 'tuesday_price',
        'wednesday_price', 'thursday_price', 'friday_price', 'saturday_price',
        'sunday_price', 'created_at', 'updated_at'
      ];

      const csvRows = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header => {
            const value = row[header];
            // Handle values that contain commas or quotes
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value || '';
          }).join(',')
        )
      ];

      const csvContent = csvRows.join('\n');
      const filename = `${hotelName.replace(/[^a-zA-Z0-9]/g, '_')}_pricing_export_${new Date().toISOString().split('T')[0]}.csv`;

      // Set response headers for file download
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', csvContent.length);

      console.log(`Export complete: ${filename} (${exportData.length} rows)`);
      return res.send(csvContent);

    } catch (error) {
      console.error("Export error:", error);
      return res.status(500).json({
        message: "Failed to export pricing data",
        error: (error as any).message
      });
    }

    // Note: Seasonal pricing export would be implemented here
    // For now, we only support base pricing export

  } catch (error) {
    console.error("Export error:", error);
    return res.status(500).json({
      message: "Failed to export pricing data",
      error: (error as any).message
    });
  }
};
